{"name": "eco-ai-preception-monitor", "version": "2.0", "description": "生物多样性智能监测系统", "author": "", "license": "MIT", "scripts": {"dev": "vite --force", "build": "vite build", "lint-fix": "eslint --fix --ext .js --ext .jsx --ext .vue src/"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.3.5", "echarts": "^5.4.2", "element-plus": "^2.3.3", "js-cookie": "^3.0.1", "json-bigint": "^1.0.0", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pinia": "^2.0.34", "qrcodejs2-fixes": "^0.0.2", "qs": "^6.11.1", "screenfull": "^6.0.2", "sortablejs": "^1.15.0", "vue": "^3.2.47", "vue-clipboard3": "^2.0.0", "vue-demi": "^0.13.11", "vue-router": "^4.1.6"}, "devDependencies": {"@types/json-bigint": "^1.0.4", "@types/node": "^18.15.11", "@types/nprogress": "^0.2.0", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "@vitejs/plugin-vue": "^4.1.0", "@vue/compiler-sfc": "^3.2.47", "eslint": "^8.38.0", "eslint-plugin-vue": "^9.10.0", "prettier": "^2.8.7", "sass": "^1.61.0", "typescript": "^5.0.4", "vite": "^4.2.1", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vue-eslint-parser": "^9.1.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bugs": {"url": "https://gitee.com/lyt-top/vue-next-admin/issues"}, "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "vuejs/vue-next-template", "element-ui", "element-plus", "vue-next-admin", "next-admin"], "repository": {"type": "git", "url": "https://gitee.com/lyt-top/vue-next-admin.git"}}